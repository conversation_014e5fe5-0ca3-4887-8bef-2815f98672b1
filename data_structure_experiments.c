#include <stdio.h>
#include <stdlib.h>

// 定义顺序表结构
#define MAXSIZE 100
typedef struct {
    int data[MAXSIZE];
    int length;
} SeqList;

// 定义单链表结点结构
typedef struct ListNode {
    int data;
    struct ListNode* next;
} ListNode;

// 定义双链表结点结构
typedef struct DListNode {
    int data;
    struct DListNode* prev;
    struct DListNode* next;
} DListNode;

// 实验1：合并顺序表中两个有序部分，使整个表有序（空间复杂度O(1)）
void mergeOrderedParts(SeqList* list, int m) {
    if (m <= 0 || m >= list->length) return;
    
    int i = 0, j = m, k = 0;
    int temp;
    
    // 使用原地归并算法
    while (i < m && j < list->length) {
        if (list->data[i] <= list->data[j]) {
            i++;
        } else {
            // 将list->data[j]插入到正确位置
            temp = list->data[j];
            // 将[i, j-1]的元素后移一位
            for (k = j; k > i; k--) {
                list->data[k] = list->data[k-1];
            }
            list->data[i] = temp;
            i++;
            j++;
            m++; // 前半部分长度增加1
        }
    }
}

// 实验2：求两个集合的交集
SeqList intersectionSets(SeqList* La, SeqList* Lb) {
    SeqList C;
    C.length = 0;
    
    for (int i = 0; i < La->length; i++) {
        for (int j = 0; j < Lb->length; j++) {
            if (La->data[i] == Lb->data[j]) {
                // 检查是否已存在于结果集合中
                int exists = 0;
                for (int k = 0; k < C.length; k++) {
                    if (C.data[k] == La->data[i]) {
                        exists = 1;
                        break;
                    }
                }
                if (!exists) {
                    C.data[C.length++] = La->data[i];
                }
                break;
            }
        }
    }
    return C;
}

// 实验3：删除单链表中值在(min, max)范围内的结点
void deleteRange(ListNode* head, int min, int max) {
    ListNode* current = head->next;
    ListNode* prev = head;
    
    while (current != NULL) {
        if (current->data > min && current->data < max) {
            prev->next = current->next;
            free(current);
            current = prev->next;
        } else {
            prev = current;
            current = current->next;
        }
    }
}

// 实验4：逆置双链表
void reverseDList(DListNode* head) {
    DListNode* current = head->next;
    DListNode* temp;
    
    // 交换头结点的prev和next指针
    temp = head->prev;
    head->prev = head->next;
    head->next = temp;
    
    // 遍历并交换每个结点的prev和next指针
    while (current != NULL) {
        temp = current->prev;
        current->prev = current->next;
        current->next = temp;
        current = current->prev; // 注意：由于指针已交换，prev指向原来的next
    }
}

// 实验5：以首结点值为基准分割单链表
void partitionList(ListNode* head) {
    if (head->next == NULL) return;
    
    int pivot = head->next->data;
    ListNode* current = head->next->next;
    ListNode* prev = head->next;
    ListNode* lessHead = head;  // 小于pivot的结点链表头
    ListNode* lessTail = head->next;  // 小于pivot的结点链表尾
    
    while (current != NULL) {
        if (current->data < pivot) {
            // 将当前结点移到小于pivot的部分
            prev->next = current->next;
            current->next = lessTail->next;
            lessTail->next = current;
            current = prev->next;
        } else {
            prev = current;
            current = current->next;
        }
    }
}
// 辅助函数：创
建单链表
ListNode* createList(int arr[], int n) {
    ListNode* head = (ListNode*)malloc(sizeof(ListNode));
    head->next = NULL;
    ListNode* tail = head;
    
    for (int i = 0; i < n; i++) {
        ListNode* newNode = (ListNode*)malloc(sizeof(ListNode));
        newNode->data = arr[i];
        newNode->next = NULL;
        tail->next = newNode;
        tail = newNode;
    }
    return head;
}

// 辅助函数：创建双链表
DListNode* createDList(int arr[], int n) {
    DListNode* head = (DListNode*)malloc(sizeof(DListNode));
    head->prev = NULL;
    head->next = NULL;
    DListNode* tail = head;
    
    for (int i = 0; i < n; i++) {
        DListNode* newNode = (DListNode*)malloc(sizeof(DListNode));
        newNode->data = arr[i];
        newNode->next = NULL;
        newNode->prev = tail;
        tail->next = newNode;
        tail = newNode;
    }
    return head;
}

// 辅助函数：打印单链表
void printList(ListNode* head) {
    ListNode* current = head->next;
    printf("链表: ");
    while (current != NULL) {
        printf("%d ", current->data);
        current = current->next;
    }
    printf("\n");
}

// 辅助函数：打印双链表
void printDList(DListNode* head) {
    DListNode* current = head->next;
    printf("双链表: ");
    while (current != NULL) {
        printf("%d ", current->data);
        current = current->next;
    }
    printf("\n");
}

// 辅助函数：打印顺序表
void printSeqList(SeqList* list) {
    printf("顺序表: ");
    for (int i = 0; i < list->length; i++) {
        printf("%d ", list->data[i]);
    }
    printf("\n");
}

// 主函数：测试所有实验
int main() {
    printf("=== 数据结构实验 ===\n\n");
    
    // 实验1测试
    printf("实验1: 合并顺序表中两个有序部分\n");
    SeqList list1;
    int arr1[] = {1, 3, 5, 7, 2, 4, 6, 8};
    list1.length = 8;
    for (int i = 0; i < 8; i++) {
        list1.data[i] = arr1[i];
    }
    printf("原始顺序表 (前4个有序，后4个有序): ");
    printSeqList(&list1);
    mergeOrderedParts(&list1, 4);
    printf("合并后的有序顺序表: ");
    printSeqList(&list1);
    printf("\n");
    
    // 实验2测试
    printf("实验2: 求两个集合的交集\n");
    SeqList La, Lb;
    int arrA[] = {1, 2, 3, 4, 5};
    int arrB[] = {3, 4, 5, 6, 7};
    La.length = 5;
    Lb.length = 5;
    for (int i = 0; i < 5; i++) {
        La.data[i] = arrA[i];
        Lb.data[i] = arrB[i];
    }
    printf("集合A: ");
    printSeqList(&La);
    printf("集合B: ");
    printSeqList(&Lb);
    SeqList C = intersectionSets(&La, &Lb);
    printf("交集C = A∩B: ");
    printSeqList(&C);
    printf("\n");
    
    // 实验3测试
    printf("实验3: 删除单链表中值在(min, max)范围内的结点\n");
    int arr3[] = {1, 3, 5, 7, 9, 2, 4, 6, 8};
    ListNode* list3 = createList(arr3, 9);
    printf("原始链表: ");
    printList(list3);
    deleteRange(list3, 3, 8);
    printf("删除值在(3, 8)范围内的结点后: ");
    printList(list3);
    printf("\n");
    
    // 实验4测试
    printf("实验4: 逆置双链表\n");
    int arr4[] = {1, 2, 3, 4, 5};
    DListNode* dlist = createDList(arr4, 5);
    printf("原始双链表: ");
    printDList(dlist);
    reverseDList(dlist);
    printf("逆置后的双链表: ");
    printDList(dlist);
    printf("\n");
    
    // 实验5测试
    printf("实验5: 以首结点值为基准分割单链表\n");
    int arr5[] = {3, 5, 4, 1, 2};
    ListNode* list5 = createList(arr5, 5);
    printf("原始链表: ");
    printList(list5);
    partitionList(list5);
    printf("以首结点值3为基准分割后: ");
    printList(list5);
    
    return 0;
}